'use client';

import { useEffect, useRef } from 'react';
import useDebouncedEffect from '~/hooks/useDebouncedEffect';
import { useAuthState, useError, useQrCodeData, useTelegramStore } from '~/stores/telegramStore';
import { AuthPassword } from '../tg/AuthPassword';
import { AuthQrCode } from '../tg/AuthQrCode';
import { TelegramBotInit } from '../tg/TelegramBotInit';
import { RemoteSingleConversation } from './RemoteSingleConversation';
import { useLocalBotParams, useTelegramBotStore } from '~/stores/telegramBotStore';
import { Loading } from '../Loading';
import { useState } from 'react';
import { BindBotManually } from '../tg/BindBotManually';

export default function TelegramRemoteConversation({ onBack }: { onBack: () => void }) {
  const { signInTelegram } = useTelegramStore();
  const { setLocalBotParams } = useTelegramBotStore();
  const localBotParams = useLocalBotParams();
  const authState = useAuthState();
  const qrCodeData = useQrCodeData();
  const error = useError();

  const authStateRef = useRef<string | null>(null);
  const [showBindBotManually, setShowBindBotManually] = useState(false);

  const botInited = localBotParams?.token;

  useEffect(() => {
    authStateRef.current = authState || null;
  }, [authState]);

  useDebouncedEffect(
    () => {
      signInTelegram();
    },
    [],
    1000
  );

  const renderContent = () => {
    if (showBindBotManually) {
      return (
        <BindBotManually
          onSuccess={info => {
            setLocalBotParams({
              token: info.apiToken,
              targetUserId: info.userId,
            });
          }}
        />
      );
    }

    if (authState === 'authorizationStateWaitQrCode' && qrCodeData) {
      return (
        <AuthQrCode
          size={200}
          className="my-qr-code"
          onBindBotManually={() => {
            setShowBindBotManually(true);
          }}
        />
      );
    }

    if (authState === 'authorizationStateWaitQrCode' && !qrCodeData) {
      return (
        <div>
          <Loading />
        </div>
      );
    }

    if (authState === 'authorizationStateWaitPassword') {
      return <AuthPassword />;
    }

    if (authState === 'authorizationStateReady') {
      return <TelegramBotInit />;
    }

    return <Loading />;
  };

  if (botInited) {
    return <RemoteSingleConversation onBack={onBack} />;
  }

  return (
    <div
      style={{
        height: '100vh',
        width: '100%',
        position: 'relative',
      }}
    >
      <div style={{}}>
        {error && <div style={{ color: 'red', marginBottom: '16px' }}>Error: {error}</div>}

        {renderContent()}
      </div>
    </div>
  );
}
