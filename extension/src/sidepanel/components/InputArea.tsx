import React, { useRef, useState, useEffect, useCallback } from 'react';
import { Tooltip } from 'antd';
import { useLanguage } from '../../utils/i18n';
import { ChatStatus, MessageContent, MessageContentPart } from '@the-agent/shared';
import FileIcon from '~/assets/icons/file.svg';

interface InputAreaProps {
  prompt: string;
  setPrompt: (prompt: string) => void;
  onSubmitRich?: (content: MessageContent) => void;
  status: ChatStatus;
  abort: () => void;
  onAttachFile?: () => void;
  onRecordAudio?: () => void;
}

export default function InputArea({
  prompt,
  setPrompt,
  onSubmitRich,
  status,
  abort,
}: InputAreaProps) {
  const { getMessage } = useLanguage();
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  type ImageAttachment = {
    id: string;
    dataUrl: string;
    size: number;
    type: string;
    width?: number;
    height?: number;
  };
  const [attachments, setAttachments] = useState<ImageAttachment[]>([]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault(); // Prevent default newline behavior
      if ((prompt.trim() || attachments.length > 0) && status === 'idle') {
        handleSend(e);
      }
    }
  };

  // use useRef and useEffect to automatically adjust textarea height
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const minHeight = 42;
      const maxHeight = 200;

      textarea.style.height = 'auto';

      const scrollHeight = textarea.scrollHeight;
      const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);

      textarea.style.height = `${newHeight}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [prompt]);

  const toCompressedDataUrl = useCallback(
    async (file: File): Promise<{ dataUrl: string; width: number; height: number }> => {
      const MAX_BYTES = 2 * 1024 * 1024; // 2MB
      const MAX_DIM = 1600; // px
      const img = await fileToImage(file);
      const { width, height } = scaleToFit(img.naturalWidth, img.naturalHeight, MAX_DIM);
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        return { dataUrl: '', width, height };
      }
      ctx.drawImage(img, 0, 0, width, height);
      const mime = file.type.startsWith('image/png') ? 'image/png' : 'image/jpeg';
      let quality = mime === 'image/jpeg' ? 0.85 : 1.0;
      let dataUrl = canvas.toDataURL(mime, quality);
      // try reduce under MAX_BYTES if needed (only for jpeg)
      let iterations = 0;
      while (mime === 'image/jpeg' && dataUrl.length * 0.75 > MAX_BYTES && iterations < 5) {
        quality = quality * 0.8;
        dataUrl = canvas.toDataURL(mime, quality);
        iterations++;
      }
      return { dataUrl, width, height };
    },
    []
  );

  const handleFiles = useCallback(
    async (fileList: File[] | FileList) => {
      const files = Array.from(fileList);
      const images = files.filter(f => f.type.startsWith('image/'));
      if (images.length === 0) return;
      const processed: ImageAttachment[] = await Promise.all(
        images.map(async file => {
          const { dataUrl, width, height } = await toCompressedDataUrl(file);
          return {
            id: `${Date.now()}_${Math.random().toString(36).slice(2)}`,
            dataUrl,
            size: file.size,
            type: file.type,
            width,
            height,
          };
        })
      );
      setAttachments(prev => [...prev, ...processed]);
    },
    [toCompressedDataUrl]
  );

  // Handle paste images
  useEffect(() => {
    const node = textareaRef.current;
    if (!node) return;
    const onPaste = async (e: ClipboardEvent) => {
      if (!e.clipboardData) return;
      const items = e.clipboardData.items;
      const files: File[] = [];
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.startsWith('image/')) {
          const file = item.getAsFile();
          if (file) files.push(file);
        }
      }
      if (files.length > 0) {
        e.preventDefault();
        await handleFiles(files);
      }
    };
    node.addEventListener('paste', onPaste as unknown as EventListener);
    return () => node.removeEventListener('paste', onPaste as unknown as EventListener);
  }, [handleFiles]);

  function openFilePicker() {
    if (status !== 'idle') return;
    fileInputRef.current?.click();
  }

  function removeAttachment(id: string) {
    setAttachments(prev => prev.filter(a => a.id !== id));
  }

  async function handleSend(e: React.FormEvent | React.MouseEvent) {
    if (!onSubmitRich) {
      throw new Error('Cannot submit message, chat handler is not set');
    }

    e.preventDefault();
    if (status !== 'idle') return;
    if (!prompt.trim() && attachments.length === 0) return;

    const parts: MessageContentPart[] = [];
    const text = prompt.trim();
    if (text) {
      parts.push({ type: 'text', text });
    }
    for (const a of attachments) {
      parts.push({ type: 'image_url', image_url: { url: a.dataUrl } });
    }
    onSubmitRich(parts);

    // Clear input
    setPrompt('');
    setAttachments([]);
  }

  function fileToImage(file: File): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = reader.result as string;
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  function scaleToFit(w: number, h: number, maxDim: number): { width: number; height: number } {
    if (w <= maxDim && h <= maxDim) return { width: w, height: h };
    const ratio = Math.min(maxDim / w, maxDim / h);
    return { width: Math.round(w * ratio), height: Math.round(h * ratio) };
  }

  return (
    <div style={{ position: 'relative' }}>
      <form
        style={{
          display: 'flex',
          flexDirection: 'column',
          padding: '0px 16px',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
            borderRadius: '18px',
            border: `1px solid ${isHovered || isFocused ? '#333333' : '#e5e7eb'}`,
            background: '#ffffff',
            overflow: 'hidden',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
            transition: 'all 0.2s ease',
          }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {attachments.length > 0 && (
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(80px, 1fr))',
                gap: '8px',
                padding: '8px',
                borderBottom: '1px solid #f3f4f6',
                backgroundColor: '#fafafa',
              }}
            >
              {attachments.map(att => (
                <div key={att.id} style={{ position: 'relative' }}>
                  <img
                    src={att.dataUrl}
                    alt="attachment"
                    style={{
                      width: '100%',
                      height: '80px',
                      objectFit: 'cover',
                      borderRadius: 8,
                      border: '1px solid #e5e7eb',
                    }}
                  />
                  <button
                    type="button"
                    onClick={() => removeAttachment(att.id)}
                    style={{
                      position: 'absolute',
                      top: 4,
                      right: 4,
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      border: 'none',
                      background: 'rgba(0,0,0,0.6)',
                      color: '#fff',
                      cursor: 'pointer',
                    }}
                    aria-label="Remove image"
                    title="Remove"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}
          <div style={{ display: 'flex', flexDirection: 'row' }}>
            <textarea
              ref={textareaRef}
              value={prompt}
              onChange={e => setPrompt(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              disabled={status !== 'idle'}
              placeholder={getMessage('typeMessage')}
              rows={1}
              style={{
                flex: 1,
                padding: '10px 16px',
                minHeight: '42px',
                maxHeight: '200px',
                height: '42px',
                outline: 'none',
                resize: 'none',
                border: 'none',
                backgroundColor: 'transparent',
                color: '#333333',
                fontSize: '13px',
                lineHeight: '1.5',
                fontWeight: 'normal',
                boxSizing: 'border-box',
                overflowY: 'auto',
              }}
            />
          </div>

          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '0 8px 6px 8px',
              gap: '8px',
            }}
          >
            <div style={{ marginLeft: '2px', display: 'flex', gap: 8, alignItems: 'center' }}>
              {/* Attach button */}
              <Tooltip title="Attach image" placement="top">
                <button
                  type="button"
                  onClick={openFilePicker}
                  disabled={status !== 'idle'}
                  style={{
                    width: 28,
                    height: 28,
                    border: 'none',
                    borderRadius: '50%',
                    backgroundColor: 'transparent',
                    transition: 'background 0.2s',
                    cursor: status !== 'idle' ? 'not-allowed' : 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onMouseOver={e => {
                    e.currentTarget.style.backgroundColor = '#E5E7EB';
                  }}
                  onMouseOut={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <img src={FileIcon} alt="Attach" style={{ width: '20px', height: '20px' }} />
                </button>
              </Tooltip>

              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                style={{ display: 'none' }}
                onChange={e => {
                  const files = (e.target.files || []) as FileList;
                  if (files.length > 0) handleFiles(files);
                  // reset so same file can be selected again
                  if (fileInputRef.current) fileInputRef.current.value = '';
                }}
              />
            </div>

            <div style={{ marginRight: '2px' }}>
              {status !== 'idle' ? (
                <button
                  type="button"
                  onClick={abort}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '30px',
                    height: '30px',
                    borderRadius: '50%',
                    backgroundColor: '#333333',
                    border: 'none',
                    cursor: 'pointer',
                    transition: 'transform 0.2s, background-color 0.2s',
                  }}
                  onMouseOver={e => {
                    e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                  }}
                  onMouseOut={e => {
                    e.currentTarget.style.backgroundColor = '#333333';
                  }}
                  onMouseDown={e => {
                    e.currentTarget.style.transform = 'scale(0.95)';
                  }}
                  onMouseUp={e => {
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                >
                  <svg
                    style={{ width: '14px', height: '14px', color: '#ffffff' }}
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <rect x="5" y="5" width="14" height="14" rx="1" />
                  </svg>
                </button>
              ) : (
                <button
                  type="submit"
                  onClick={handleSend}
                  disabled={!prompt.trim() && attachments.length === 0}
                  aria-label={getMessage('send')}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '30px',
                    height: '30px',
                    borderRadius: '50%',
                    backgroundColor:
                      !prompt.trim() && attachments.length === 0 ? '#e5e7eb' : '#333333',
                    border: 'none',
                    cursor: !prompt.trim() && attachments.length === 0 ? 'not-allowed' : 'pointer',
                    opacity: !prompt.trim() && attachments.length === 0 ? 0.5 : 1,
                    transition: 'transform 0.2s, background-color 0.2s',
                    padding: '5px',
                  }}
                  onMouseOver={e => {
                    if (prompt.trim() || attachments.length > 0) {
                      e.currentTarget.style.backgroundColor = '#000000';
                    }
                  }}
                  onMouseOut={e => {
                    if (prompt.trim() || attachments.length > 0) {
                      e.currentTarget.style.backgroundColor = '#333333';
                    }
                  }}
                  onMouseDown={e => {
                    if (prompt.trim() || attachments.length > 0) {
                      e.currentTarget.style.transform = 'scale(0.95)';
                    }
                  }}
                  onMouseUp={e => {
                    if (prompt.trim() || attachments.length > 0) {
                      e.currentTarget.style.transform = 'scale(1)';
                    }
                  }}
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="text-white dark:text-black"
                  >
                    <path
                      d="M7 11L12 6L17 11M12 18V7"
                      stroke="white"
                      strokeWidth="1.8"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    ></path>
                  </svg>
                </button>
              )}
            </div>
          </div>
        </div>

        <p
          style={{
            fontSize: '12px',
            marginTop: '8px',
            textAlign: 'center',
            color: '#6b7280',
          }}
        >
          {getMessage('privacyDisclaimer')}
        </p>
      </form>
    </div>
  );
}
