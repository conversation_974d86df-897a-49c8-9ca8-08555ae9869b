import { User as UserIcon } from 'lucide-react';
import { db, UserInfo } from '~/storages/indexdb';
import { useState, useEffect } from 'react';
import { Modal, Dropdown, Tooltip } from 'antd';
import { ProviderGroup } from './ModelCascader';
import { useLiveQuery } from 'dexie-react-hooks';
import { Model } from '~/types';
import { API_KEY_TAG, SYSTEM_MODEL_ID } from '~/configs/common';
import miniappIcon from '~/assets/icons/miniapp.svg';
import LanguageSelector from './LanguageSelector';
import { useLanguage } from '~/utils/i18n';
import { useNavigate, useLocation } from 'react-router-dom';
import homeIcon from '~/assets/icons/home.svg';
import workflowIcon from '~/assets/icons/workflow.svg';
import remoteIcon from '~/assets/icons/remote.svg';
import testIcon from '~/assets/icons/test.svg';
import chatlistIcon from '~/assets/icons/chatlist.svg';
import dependencyIcon from '~/assets/icons/dependency.svg';
import DependencyModal from './modals/dependencyModal';

interface HeaderProps {
  user?: UserInfo | null;
  setShowConversationList: (show: boolean) => void;
  setShowMiniappApplicationsList?: (show: boolean) => void;
  setShowWorkflowConversationList?: (show: boolean) => void;
  setShowTelegramRemoteConversation?: (show: boolean) => void;
}

interface ModelGroup {
  type: string;
  models: Model[];
}

const Header = ({
  user,
  setShowConversationList,
  setShowMiniappApplicationsList,
  setShowWorkflowConversationList,
  setShowTelegramRemoteConversation,
}: HeaderProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [providerGroups, setProviderGroups] = useState<ProviderGroup[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [, setSelectedModelId] = useState<string>('');
  const [apiKeyInput, setApiKeyInput] = useState('');
  const [editingProvider] = useState<string | null>(null);
  const [apiModalOpen, setApiModalOpen] = useState(false);
  const [showDependencyModal, setShowDependencyModal] = useState(false);
  const [hasDependencySelection, setHasDependencySelection] = useState(false);
  const { getMessage } = useLanguage();

  // Navigation state
  const currentPath = location.pathname;
  const isChat = currentPath === '/conversation';
  const isScript = currentPath === '/miniapp';
  const isWorkflow = currentPath === '/workflow';
  const isRemote = currentPath === '/remote';

  const models = useLiveQuery(() => (user?.id ? db.getUserModels(user.id) : []), [user?.id]);

  // Navigation handlers - only navigate, don't show lists
  const handleChatClick = () => {
    navigate('/conversation');
  };

  const handleScriptClick = async () => {
    if (!user?.id) {
      navigate('/miniapp');
      return;
    }

    try {
      // Check if there's a latest non-archived conversation for userScript type
      const latest = await db.getLatestConversationForMiniappType(user.id, 'userScript', false);

      if (latest) {
        // Update conversation's last_selected_at to mark it as recently accessed
        await db.updateConversation(latest.conversation.id, { last_selected_at: Date.now() });
        // Navigate with state to auto-select this miniapp
        navigate('/miniapp', { state: { autoSelectMiniapp: latest.miniapp } });
      } else {
        // No existing conversation, navigate normally (will create new miniapp)
        navigate('/miniapp');
      }
    } catch (error) {
      console.error('Error handling script click:', error);
      navigate('/miniapp');
    }
  };

  const handleWorkflowClick = async () => {
    if (!user?.id) {
      navigate('/workflow');
      return;
    }

    try {
      // Check if there's a latest non-archived conversation for workflow type
      const latest = await db.getLatestConversationForMiniappType(user.id, 'workflow', false);

      if (latest) {
        // Update conversation's last_selected_at to mark it as recently accessed
        await db.updateConversation(latest.conversation.id, { last_selected_at: Date.now() });
        // Navigate with state to auto-select this miniapp
        navigate('/workflow', { state: { autoSelectMiniapp: latest.miniapp } });
      } else {
        // No existing conversation, navigate normally (will create new miniapp)
        navigate('/workflow');
      }
    } catch (error) {
      console.error('Error handling workflow click:', error);
      navigate('/workflow');
    }
  };

  const handleRemoteClick = () => {
    if (isRemote) {
      // If already on remote page, go back to conversation
      navigate('/conversation');
    } else {
      // If on conversation page, go to remote
      navigate('/remote');
    }
  };

  // Floating chat list handlers
  const handleChatListClick = () => {
    if (isChat) {
      setShowConversationList?.(true);
    } else if (isScript) {
      setShowMiniappApplicationsList?.(true);
    } else if (isWorkflow) {
      setShowWorkflowConversationList?.(true);
    } else if (isRemote) {
      setShowTelegramRemoteConversation?.(true);
    }
  };

  const handleTestClick = () => {
    navigate('/test-webtoolkit');
  };

  useEffect(() => {
    const init = async () => {
      // build fullProviderGroups (with full model information)
      const fullGroups: Record<string, ModelGroup> = {};
      (models ?? []).forEach(model => {
        if (!fullGroups[model.type]) {
          fullGroups[model.type] = {
            type: model.type,
            models: [],
          };
        }
        fullGroups[model.type].models.push(model);
      });
      const fullProviderGroups = Object.values(fullGroups);

      const providerGroups = fullProviderGroups.map((g: ModelGroup) => ({
        type: g.type === 'system' ? 'Default' : g.type,
        models: g.models,
      }));
      setProviderGroups(providerGroups);

      // default selected Default provider and systemModelId
      let defaultProvider = 'Default';
      let defaultModelId = SYSTEM_MODEL_ID;

      // if there is Default provider
      const defaultGroup = providerGroups.find(g => g.type === 'Default');
      if (defaultGroup) {
        defaultProvider = defaultGroup.type;
        const systemModel = defaultGroup.models.find((m: Model) => m.id === SYSTEM_MODEL_ID);
        if (systemModel) {
          defaultModelId = SYSTEM_MODEL_ID;
        } else if (defaultGroup.models.length > 0) {
          defaultModelId = defaultGroup.models[0].id;
        }
      } else if (providerGroups.length > 0) {
        defaultProvider = providerGroups[0].type;
        defaultModelId = providerGroups[0].models[0]?.id;
      }

      if (user?.selectedModelId) {
        providerGroups.forEach(group => {
          const match = group.models.find((m: Model) => m.id === user.selectedModelId);
          if (match) {
            defaultProvider = group.type;
            defaultModelId = match.id;
          }
        });
      }

      setSelectedProvider(defaultProvider);
      setSelectedModelId(defaultModelId);
    };
    init();
  }, [user, models]);

  // When provider changes, update model selection
  useEffect(() => {
    if (!selectedProvider && providerGroups.length > 0) {
      setSelectedProvider(providerGroups[0].type);
    }
    const group = providerGroups.find(g => g.type === selectedProvider);
    if (group && group.models.length > 0) {
      setSelectedModelId(group.models[0].id);
    }
  }, [selectedProvider, providerGroups]);

  const handleApiKeySave = async () => {
    try {
      // Update all models for this provider with the new API key
      const user = await db.getCurrentUser();
      if (user) {
        const userModels = await db.getUserModels(user.id);
        const modelsToUpdate = userModels.filter((m: Model) => m.type === editingProvider);
        for (const model of modelsToUpdate) {
          await db.addOrUpdateModel({
            ...model,
            apiKey: apiKeyInput,
            type: editingProvider || '',
            userId: user.id,
          });
        }
        // Refresh provider groups
        const groups: Record<string, ProviderGroup> = {};
        userModels.forEach((model: Model) => {
          if (!groups[model.type]) {
            groups[model.type] = {
              type: model.type,
              models: [],
            };
          }
          groups[model.type].models.push({ id: model.id, name: model.name });
        });
        setProviderGroups(Object.values(groups));
      }
      setApiModalOpen(false);
    } catch (error) {
      console.error('Failed to save API key:', error);
    }
  };

  // custom dropdown menu
  const dropdownMenu = (
    <div
      style={{
        minWidth: 200,
        background: '#fff',
        borderRadius: 18,
        boxShadow: '0 2px 12px 0 rgba(0,0,0,0.30)',
        padding: '18px 18px 12px 18px',
        display: 'flex',
        flexDirection: 'column',
        gap: 12,
      }}
    >
      <div
        style={{
          color: '#bdbdbd',
          fontSize: 14,
          marginBottom: 8,
          textAlign: 'center',
          wordBreak: 'break-all',
        }}
      >
        {user?.email || user?.username || getMessage('user')}
      </div>

      <button
        style={{
          width: '100%',
          background: '#232323',
          color: '#fff',
          border: '2px solid #232323',
          borderRadius: 12,
          fontSize: 14,
          padding: '8px 0',
          marginTop: 2,
          marginBottom: 2,
          cursor: 'pointer',
          boxShadow: '0 1px 2px 0 rgba(0,0,0,0.04)',
          transition: 'background 0.18s, color 0.18s',
          textAlign: 'center',
        }}
        onClick={() => {
          window.open(process.env.PLASMO_PUBLIC_WEB_URL || '', '_blank');
        }}
      >
        {getMessage('viewProfile')}
      </button>
      <button
        style={{
          width: '100%',
          background: '#232323',
          color: '#fff',
          border: '2px solid #232323',
          borderRadius: 12,
          fontSize: 14,
          padding: '8px 0',
          marginTop: 2,
          marginBottom: 2,
          cursor: 'pointer',
          boxShadow: '0 1px 2px 0 rgba(0,0,0,0.04)',
          transition: 'background 0.18s, color 0.18s',
          textAlign: 'center',
        }}
        onClick={async () => {
          if (!user) {
            return;
          }
          // Directly update user's active status without using saveOrUpdateUser
          await db.users.update(user.id, { active: false });
          await chrome.storage.local.remove(API_KEY_TAG);
          navigate('/');
        }}
      >
        {getMessage('logout')}
      </button>
    </div>
  );

  const buttonStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    border: 'none',
    borderRadius: '50%',
    padding: 0,
    cursor: 'pointer',
    flexShrink: 0, // Prevent buttons from shrinking in the scrollable container
  };

  return (
    <>
      {/* Main Header */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#ffffff',
          padding: '0 16px',
          height: '44px',
          borderBottom: '1px solid #f0f0f0',
          position: 'relative',
        }}
      >
        {/* Navigation Tabs */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            height: '44px',
            gap: '2px',
            position: 'relative',
          }}
        >
          {/* Home Tab */}
          <div style={{ position: 'relative' }}>
            <Tooltip title={getMessage('chat')} placement="bottom">
              <button
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: 'none',
                  backgroundColor: 'transparent',
                  padding: '12px 8px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                }}
                onClick={handleChatClick}
              >
                <img
                  src={homeIcon}
                  alt="Home"
                  style={{
                    width: 20,
                    height: 20,
                  }}
                />
              </button>
            </Tooltip>
            {(isChat || isRemote) && (
              <div
                style={{
                  position: 'absolute',
                  bottom: '-1px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '24px',
                  height: '3px',
                  backgroundColor: '#000000',
                  borderRadius: '2px 2px 0 0',
                }}
              />
            )}
          </div>

          {/* Script Tab */}
          <div style={{ position: 'relative' }}>
            <Tooltip title={getMessage('script')} placement="bottom">
              <button
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: 'none',
                  backgroundColor: 'transparent',
                  padding: '12px 8px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                }}
                onClick={handleScriptClick}
              >
                <img
                  src={miniappIcon}
                  alt="Script"
                  style={{
                    width: 20,
                    height: 20,
                  }}
                />
              </button>
            </Tooltip>
            {isScript && (
              <div
                style={{
                  position: 'absolute',
                  bottom: '-1px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '24px',
                  height: '3px',
                  backgroundColor: '#000000',
                  borderRadius: '2px 2px 0 0',
                }}
              />
            )}
          </div>

          {/* Workflow Tab */}
          <div style={{ position: 'relative' }}>
            <Tooltip title={getMessage('workflow')} placement="bottom">
              <button
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: 'none',
                  backgroundColor: 'transparent',
                  padding: '12px 8px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                }}
                onClick={handleWorkflowClick}
              >
                <img
                  src={workflowIcon}
                  alt="Workflow"
                  style={{
                    width: 20,
                    height: 20,
                  }}
                />
              </button>
            </Tooltip>
            {isWorkflow && (
              <div
                style={{
                  position: 'absolute',
                  bottom: '-1px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '24px',
                  height: '3px',
                  backgroundColor: '#000000',
                  borderRadius: '2px 2px 0 0',
                }}
              />
            )}
          </div>
        </div>

        {/* Right Side */}
        <div
          className="header-right-side"
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '2px',
            maxWidth: '100px', // Width for exactly 3 icons: (32px × 3) + (2px × 2) = 100px
            overflowX: 'auto',
            overflowY: 'hidden',
            scrollbarWidth: 'thin',
            scrollbarColor: '#d1d5db #f9fafb',
            // Smooth scrolling
            scrollBehavior: 'smooth',
            // Prevent flex shrinking of icons
            flexShrink: 0,
          }}
        >
          {/* Dependency icon - show when isChat or isWorkflow */}
          {(isChat || isWorkflow) && (
            <Tooltip title="Workflow project as dependency" placement="bottom">
              <button
                style={{
                  ...buttonStyle,
                  width: 32,
                  height: 32,
                  backgroundColor: hasDependencySelection ? '#E5E7EB' : 'transparent',
                  transition: 'background 0.2s',
                }}
                onClick={() => setShowDependencyModal(true)}
              >
                <img
                  src={dependencyIcon}
                  alt="Dependency"
                  style={{
                    width: 20,
                    height: 20,
                  }}
                />
              </button>
            </Tooltip>
          )}

          {/* Remote icon - show when isChat or isRemote */}
          {(isChat || isRemote) && (
            <Tooltip title={getMessage('remote')} placement="bottom">
              <button
                style={{
                  ...buttonStyle,
                  width: 32,
                  height: 32,
                  backgroundColor: isRemote ? '#E5E7EB' : 'transparent',
                  transition: 'background 0.2s',
                }}
                onClick={handleRemoteClick}
              >
                <img
                  src={remoteIcon}
                  alt="Remote"
                  style={{
                    width: 20,
                    height: 20,
                  }}
                />
              </button>
            </Tooltip>
          )}

          {/* Test WebToolkit Entry - only show in development */}
          {process.env.NODE_ENV !== 'production' && (
            <Tooltip title={getMessage('test')} placement="bottom">
              <button
                style={{
                  ...buttonStyle,
                  width: 32,
                  height: 32,
                  backgroundColor: 'transparent',
                  transition: 'background 0.2s',
                }}
                onClick={handleTestClick}
              >
                <img
                  src={testIcon}
                  alt="Test"
                  style={{
                    width: 20,
                    height: 20,
                  }}
                />
              </button>
            </Tooltip>
          )}

          <Tooltip title="Chat List" placement="bottom">
            <button
              style={{
                ...buttonStyle,
                width: 32,
                height: 32,
                backgroundColor: 'transparent',
                transition: 'background 0.2s',
              }}
              onClick={handleChatListClick}
            >
              <img
                src={chatlistIcon}
                alt="Chat List"
                style={{
                  width: 20,
                  height: 20,
                }}
              />
            </button>
          </Tooltip>

          <Dropdown popupRender={() => dropdownMenu} trigger={['click']} placement="bottomRight">
            <Tooltip title={getMessage('user')} placement="bottom">
              <button
                style={{
                  ...buttonStyle,
                  width: 32,
                  height: 32,
                  backgroundColor: 'transparent',
                  transition: 'background 0.2s',
                }}
              >
                {user?.photoURL ? (
                  <img
                    src={user.photoURL}
                    alt={user.username || getMessage('user')}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      borderRadius: '50%',
                      display: 'block',
                    }}
                  />
                ) : (
                  <UserIcon size={20} />
                )}
              </button>
            </Tooltip>
          </Dropdown>
          <LanguageSelector />
        </div>
      </div>

      {/* API Key Modal */}
      <Modal
        open={apiModalOpen}
        onCancel={() => setApiModalOpen(false)}
        footer={null}
        centered
        closable
        style={{
          background: '#fff',
          borderRadius: 10,
          color: '#111',
        }}
      >
        <div style={{ fontSize: 15, fontWeight: 700, marginBottom: 10 }}>
          {getMessage('setApiKey')}:
          {editingProvider
            ? editingProvider.charAt(0).toUpperCase() + editingProvider.slice(1)
            : ''}
        </div>

        <div
          style={{
            width: '100%',
            gap: 12,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <input
            placeholder={getMessage(
              'enterApiKey',
              editingProvider
                ? editingProvider.charAt(0).toUpperCase() + editingProvider.slice(1)
                : 'API'
            )}
            value={apiKeyInput}
            onChange={e => setApiKeyInput(e.target.value)}
            style={{
              width: '100%',
              height: 32,
              borderRadius: 6,
              border: '1px solid #e5e7eb',
              background: '#fafafa',
              color: '#111',
              padding: '0 8px',
              marginBottom: 10,
              fontSize: 13,
              boxSizing: 'border-box',
            }}
          />
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: 0,
              width: '100%',
            }}
          >
            <button
              style={{
                background: '#22c55e',
                color: '#fff',
                border: 'none',
                borderRadius: 6,
                padding: '7px 0',
                fontWeight: 600,
                fontSize: 14,
                cursor: 'pointer',
                width: '100%',
                marginBottom: 8,
                boxSizing: 'border-box',
              }}
              onClick={handleApiKeySave}
            >
              {getMessage('save')}
            </button>
            <button
              style={{
                background: '#dc2626',
                color: '#fff',
                border: 'none',
                borderRadius: 6,
                padding: '7px 0',
                fontWeight: 600,
                fontSize: 14,
                cursor: 'pointer',
                width: '100%',
                boxSizing: 'border-box',
              }}
              onClick={() => setApiModalOpen(false)}
            >
              {getMessage('cancel')}
            </button>
          </div>
        </div>
      </Modal>

      {/* Dependency Modal */}
      <DependencyModal
        open={showDependencyModal}
        onClose={() => setShowDependencyModal(false)}
        onSelectionChange={setHasDependencySelection}
      />
    </>
  );
};

export default Header;
